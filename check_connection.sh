#!/bin/bash

# 检查连接的是 predixy 还是直接 Redis

PREDIXY_PORT=6379  # 修改为你的端口
REDIS_CLI="redis-cli -p $PREDIXY_PORT"

echo "=== 连接检查 ==="
echo "端口: $PREDIXY_PORT"
echo

# 1. 基本连接测试
echo "1. 测试连接..."
if ! $REDIS_CLI ping &>/dev/null; then
    echo "❌ 连接失败"
    exit 1
fi
echo "✅ 连接成功"
echo

# 2. 检查 INFO 输出
echo "2. 检查服务器信息..."
info_output=$($REDIS_CLI INFO 2>&1)
echo "INFO 输出:"
echo "$info_output"
echo

# 3. 检查是否是 predixy
echo "3. 判断服务器类型..."
if [[ $info_output == *"predixy"* ]] || [[ $info_output == *"Predixy"* ]]; then
    echo "✅ 连接到 Predixy 代理"
elif [[ $info_output == *"redis_version"* ]]; then
    echo "⚠️  连接到直接 Redis 服务器"
    echo "   如果你想测试 predixy，请确认："
    echo "   1. predixy 是否在运行"
    echo "   2. 端口是否正确 (通常是 7617)"
    echo "   3. 配置是否正确"
else
    echo "❓ 未知服务器类型"
fi
echo

# 4. 测试几个关键命令来确认
echo "4. 测试关键命令..."

# TIME 命令 - predixy 通常不支持
echo -n "TIME 命令: "
if time_output=$($REDIS_CLI TIME 2>&1); then
    if [[ $time_output == *"unknown command"* ]]; then
        echo "❌ 不支持 (可能是 predixy)"
    else
        echo "✅ 支持 (可能是直接 Redis)"
    fi
else
    echo "❌ 执行失败"
fi

# CONFIG RESETSTAT - predixy 特有命令
echo -n "CONFIG RESETSTAT 命令: "
if resetstat_output=$($REDIS_CLI CONFIG RESETSTAT 2>&1); then
    if [[ $resetstat_output == "OK" ]]; then
        echo "✅ 支持 (可能是 predixy)"
    else
        echo "⚠️  返回: $resetstat_output"
    fi
else
    echo "❌ 不支持"
fi

# ROLE 命令 - predixy 通常不支持
echo -n "ROLE 命令: "
if role_output=$($REDIS_CLI ROLE 2>&1); then
    if [[ $role_output == *"unknown command"* ]]; then
        echo "❌ 不支持 (可能是 predixy)"
    else
        echo "✅ 支持 (可能是直接 Redis)"
    fi
else
    echo "❌ 执行失败"
fi

echo
echo "=== 结论 ==="
if [[ $info_output == *"predixy"* ]] || [[ $info_output == *"Predixy"* ]]; then
    echo "🎯 你连接的是 Predixy 代理"
    echo "   可以继续进行 predixy 命令支持测试"
else
    echo "⚠️  你可能连接的是直接 Redis 服务器"
    echo "   建议检查："
    echo "   - predixy 服务是否启动"
    echo "   - 端口配置是否正确"
    echo "   - 如果 predixy 在 7617 端口，请修改脚本中的 PREDIXY_PORT=7617"
fi
