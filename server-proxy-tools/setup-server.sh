#!/bin/bash
#
# 服务器代理配置脚本
# 自动配置服务器的代理环境变量
#
# 使用方法：
#     ./setup-server.sh [MAC_IP]
#
# 参数：
#     MAC_IP: Mac机器的VPN IP地址，默认从SSH_CLIENT环境变量获取
#

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取Mac IP地址
get_mac_ip() {
    if [[ $# -gt 0 ]]; then
        echo "$1"
    elif [[ -n "${SSH_CLIENT:-}" ]]; then
        echo "${SSH_CLIENT%% *}"
    elif [[ -n "${SSH_CONNECTION:-}" ]]; then
        # SSH_CONNECTION格式: "client_ip client_port server_ip server_port"
        echo "${SSH_CONNECTION%% *}"
    else
        # 尝试从当前SSH连接中获取
        local ssh_ip
        ssh_ip=$(who am i 2>/dev/null | grep -oE '\([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+\)' | tr -d '()')
        if [[ -n "$ssh_ip" ]]; then
            echo "$ssh_ip"
        else
            # 尝试从netstat获取SSH连接
            ssh_ip=$(netstat -tn 2>/dev/null | grep ':22 ' | grep ESTABLISHED | head -1 | awk '{print $5}' | cut -d: -f1)
            if [[ -n "$ssh_ip" && "$ssh_ip" != "127.0.0.1" ]]; then
                echo "$ssh_ip"
            else
                return 1
            fi
        fi
    fi
}

# 测试代理连接
test_proxy() {
    local proxy_url="$1"
    log "测试智能代理连接: $proxy_url"

    local domestic_success=false
    local foreign_success=false

    # 测试国内网站（应该能直连）
    log "测试国内网站访问..."
    if curl -I --connect-timeout 5 --proxy "$proxy_url" http://www.baidu.com >/dev/null 2>&1; then
        log "✅ 国内网站访问正常"
        domestic_success=true
    else
        error "❌ 国内网站访问失败"
    fi

    # 测试国外网站（需要科学上网）
    log "测试国外网站访问..."
    if curl -I --connect-timeout 10 --proxy "$proxy_url" https://www.google.com >/dev/null 2>&1; then
        log "✅ 国外网站访问正常（科学上网可用）"
        foreign_success=true
    else
        warn "⚠️  国外网站访问失败（科学上网不可用）"
        warn "    这不影响国内网站的正常使用"
    fi

    # 至少国内网站要能访问
    if [ "$domestic_success" = true ]; then
        if [ "$foreign_success" = true ]; then
            log "🌍 完整网络访问能力：国内网站 + 国外网站"
        else
            log "🌏 基础网络访问能力：仅国内网站"
        fi
        return 0
    else
        error "❌ 代理连接测试失败：无法访问任何网站"
        return 1
    fi
}

# 配置代理环境变量
setup_proxy() {
    local mac_ip="$1"
    local proxy_url="http://${mac_ip}:8888"
    
    log "配置代理环境变量..."
    
    # 设置当前会话的代理
    export http_proxy="$proxy_url"
    export https_proxy="$proxy_url"
    export HTTP_PROXY="$proxy_url"
    export HTTPS_PROXY="$proxy_url"
    export no_proxy="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
    export NO_PROXY="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
    
    log "当前会话代理已配置: $proxy_url"

    # 提示用户如何在当前shell中应用环境变量
    echo
    echo -e "${YELLOW}注意：要在当前shell中应用代理设置，请执行以下命令：${NC}"
    echo -e "${BLUE}source <(./setup-server.sh --export-env)${NC}"
    echo -e "或者："
    echo -e "${BLUE}eval \"\$(./setup-server.sh --export-env)\"${NC}"
    echo
    
    # 询问是否永久配置
    echo
    read -p "是否永久配置代理到 ~/.bashrc? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "配置永久代理..."
        
        # 备份原始bashrc
        if [[ -f ~/.bashrc ]]; then
            cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
            log "已备份 ~/.bashrc"
        fi
        
        # 添加代理配置
        cat >> ~/.bashrc << EOF

# 代理配置 - 由 setup-server.sh 添加
export http_proxy="$proxy_url"
export https_proxy="$proxy_url"
export HTTP_PROXY="$proxy_url"
export HTTPS_PROXY="$proxy_url"
export no_proxy="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
export NO_PROXY="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
EOF
        
        log "✅ 永久代理配置已添加到 ~/.bashrc"
        log "下次登录时自动生效，或执行: source ~/.bashrc"
    fi
}

# 测试网络访问
test_network() {
    log "测试网络访问..."

    echo
    log "测试 Google 访问:"
    if curl -I --connect-timeout 10 https://www.google.com >/dev/null 2>&1; then
        log "✅ Google 访问成功"
    else
        error "❌ Google 访问失败"
    fi

    echo
    log "测试 GitHub 访问:"
    if curl -I --connect-timeout 10 https://github.com >/dev/null 2>&1; then
        log "✅ GitHub 访问成功"
    else
        error "❌ GitHub 访问失败"
    fi
}

# 显示使用说明
show_usage() {
    local mac_ip="$1"
    local proxy_url="http://${mac_ip}:8888"

    echo
    log "代理配置完成！"
    echo
    echo -e "${YELLOW}⚠️  重要提示：要在当前shell中使用代理，请执行：${NC}"
    echo -e "${GREEN}eval \"\$(./setup-server.sh --export-env)\"${NC}"
    echo
    echo -e "${BLUE}常用测试命令:${NC}"
    echo "  curl -I https://www.google.com"
    echo "  curl -I https://github.com"
    echo "  git clone https://github.com/用户名/仓库名.git"
    echo "  pip install 包名"
    echo
    echo -e "${BLUE}如果环境变量代理不工作，使用显式代理:${NC}"
    echo "  curl -I --proxy $proxy_url https://www.google.com"
    echo "  curl -I --proxy $proxy_url https://github.com"
    echo
    echo -e "${BLUE}检查代理状态:${NC}"
    echo "  env | grep -i proxy"
    echo "  curl -I --proxy $proxy_url https://httpbin.org/ip"
    echo
    echo -e "${BLUE}取消代理:${NC}"
    echo "  unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY"
    echo
}

# 输出环境变量设置命令
export_env() {
    local mac_ip
    if ! mac_ip=$(get_mac_ip "$@"); then
        echo "# 错误：无法获取Mac IP地址，请手动指定" >&2
        echo "# 使用方法: $0 --export-env <MAC_IP>" >&2
        return 1
    fi

    local proxy_url="http://${mac_ip}:8888"

    # 输出可以被eval的环境变量设置命令
    cat << EOF
export http_proxy="$proxy_url"
export https_proxy="$proxy_url"
export HTTP_PROXY="$proxy_url"
export HTTPS_PROXY="$proxy_url"
export no_proxy="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
export NO_PROXY="localhost,127.0.0.1,::1,10.*,172.*,192.168.*,.local"
echo "代理环境变量已设置: $proxy_url"
EOF
}

# 主函数
main() {
    # 检查是否是导出环境变量模式
    if [[ "${1:-}" == "--export-env" ]]; then
        shift
        export_env "$@"
        return $?
    fi

    echo "============================================================"
    log "服务器代理配置脚本"
    echo "============================================================"

    # 获取Mac IP
    if ! mac_ip=$(get_mac_ip "$@"); then
        error "无法获取Mac IP地址"
        error "调试信息:"
        error "  SSH_CLIENT: ${SSH_CLIENT:-未设置}"
        error "  SSH_CONNECTION: ${SSH_CONNECTION:-未设置}"
        error "  当前用户: $(whoami)"
        error "  who am i: $(who am i 2>/dev/null || echo '无法获取')"
        error ""
        error "请手动指定: $0 <MAC_IP>"
        error "例如: $0 *************"
        exit 1
    fi
    
    log "检测到Mac IP: $mac_ip"
    
    # 测试代理连接
    if ! test_proxy "http://${mac_ip}:8888"; then
        error "请确保Mac上的代理服务器正在运行:"
        error "  python3 mac-proxy-server.py"
        exit 1
    fi
    
    # 配置代理
    setup_proxy "$mac_ip"
    
    # 测试网络访问
    test_network
    
    # 显示使用说明
    show_usage "$mac_ip"
    
    log "🎉 配置完成！"
}

# 执行主函数
main "$@"
