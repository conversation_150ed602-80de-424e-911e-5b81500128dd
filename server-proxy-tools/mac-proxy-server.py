#!/usr/bin/env python3
"""
Mac端智能代理服务器
为服务器提供外网访问代理服务，支持智能路由和回退机制

功能：
- 国内网站：优先直连，提高速度
- 国外网站：通过FlClash科学上网
- 智能回退：FlClash不可用时仍可访问国内网站

使用方法：
    python3 mac-proxy-server.py

端口：8888
"""

import http.server
import socketserver
import urllib.request
import socket
import threading
import select
import sys
import json
import re
from urllib.parse import urlparse

class SiteClassifier:
    """网站分类器：区分国内外网站"""

    def __init__(self):
        # 国外网站（需要科学上网）
        self.foreign_sites = [
            'google.com', 'googleapis.com', 'googleusercontent.com',
            'github.com', 'githubusercontent.com',
            'stackoverflow.com', 'stackexchange.com',
            'youtube.com', 'youtu.be',
            'facebook.com', 'twitter.com', 'instagram.com',
            'wikipedia.org', 'wikimedia.org',
            'reddit.com', 'medium.com',
            'dropbox.com', 'onedrive.com',
            'netflix.com', 'spotify.com',
            'apple.com', 'icloud.com',
            'microsoft.com', 'office.com',
            'amazon.com', 'aws.amazon.com',
            'cloudflare.com', 'jsdelivr.net'
        ]

        # 国内网站（可以直连）
        self.domestic_sites = [
            'baidu.com', 'qq.com', 'taobao.com', 'tmall.com',
            'weibo.com', 'sina.com.cn', 'sohu.com',
            'aliyun.com', 'alipay.com', 'tencent.com',
            'jd.com', 'pinduoduo.com', 'meituan.com',
            'douban.com', 'zhihu.com', 'bilibili.com',
            'csdn.net', 'cnblogs.com', 'oschina.net',
            '163.com', '126.com', 'yeah.net',
            'gitee.com', 'coding.net',
            'huawei.com', 'xiaomi.com', 'oppo.com'
        ]

    def classify_host(self, host):
        """分类主机名"""
        host_lower = host.lower()

        # 检查是否为国外网站
        for site in self.foreign_sites:
            if site in host_lower or host_lower.endswith('.' + site):
                return 'foreign'

        # 检查是否为国内网站
        for site in self.domestic_sites:
            if site in host_lower or host_lower.endswith('.' + site):
                return 'domestic'

        # 未知网站
        return 'unknown'

class ProxyDetector:
    """代理检测器：检测FlClash可用性"""

    def __init__(self):
        self.proxy_ports = [1086, 7890, 7891]  # 常见代理端口
        self.last_working_port = None

    def is_flclash_available(self):
        """检测FlClash是否可用"""
        # 如果有上次工作的端口，优先测试
        if self.last_working_port:
            if self._test_port(self.last_working_port):
                return True

        # 测试所有端口
        for port in self.proxy_ports:
            if self._test_port(port):
                self.last_working_port = port
                return True

        return False

    def get_proxy_port(self):
        """获取可用的代理端口"""
        return self.last_working_port or 1086

    def _test_port(self, port):
        """测试端口是否可用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result == 0
        except:
            return False

class SmartRouter:
    """智能路由器：决定使用哪种连接方式"""

    def __init__(self):
        self.classifier = SiteClassifier()
        self.detector = ProxyDetector()

    def get_proxy_strategy(self, host):
        """获取代理策略"""
        site_type = self.classifier.classify_host(host)

        if site_type == 'foreign':
            return 'proxy_required'  # 国外网站必须走代理
        elif site_type == 'domestic':
            return 'direct_preferred'  # 国内网站优先直连
        else:
            return 'auto'  # 未知网站自动判断

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.router = SmartRouter()
        super().__init__(*args, **kwargs)

    def do_GET(self):
        self.proxy_request()

    def do_POST(self):
        self.proxy_request()

    def do_HEAD(self):
        self.proxy_request()

    def get_full_url(self):
        """获取完整URL"""
        if self.path.startswith('http'):
            return self.path
        else:
            host = self.headers.get('Host', '')
            return 'http://' + host + self.path

    def extract_host(self, url):
        """从URL中提取主机名"""
        try:
            if url.startswith('http'):
                parsed = urlparse(url)
                return parsed.netloc
            else:
                return self.headers.get('Host', '').split(':')[0]
        except:
            return ''

    def try_direct_connection(self, url):
        """尝试直连"""
        print(f'尝试直连: {url}')
        req = urllib.request.Request(url, method=self.command)

        for header, value in self.headers.items():
            if header.lower() not in ['host', 'connection', 'proxy-connection']:
                req.add_header(header, value)

        response = urllib.request.urlopen(req, timeout=10)
        return response

    def try_proxy_connection(self, url):
        """尝试通过FlClash代理连接"""
        proxy_port = self.router.detector.get_proxy_port()
        print(f'通过FlClash代理连接: {url} (端口: {proxy_port})')

        proxy_handler = urllib.request.ProxyHandler({
            'http': f'http://127.0.0.1:{proxy_port}',
            'https': f'http://127.0.0.1:{proxy_port}'
        })
        opener = urllib.request.build_opener(proxy_handler)

        req = urllib.request.Request(url, method=self.command)
        for header, value in self.headers.items():
            if header.lower() not in ['host', 'connection', 'proxy-connection']:
                req.add_header(header, value)

        response = opener.open(req, timeout=30)
        return response

    def proxy_request(self):
        try:
            url = self.get_full_url()
            host = self.extract_host(url)
            strategy = self.router.get_proxy_strategy(host)

            print(f'处理请求: {url} (主机: {host}, 策略: {strategy})')

            response = None

            if strategy == 'proxy_required':
                # 国外网站：必须走代理
                if self.router.detector.is_flclash_available():
                    response = self.try_proxy_connection(url)
                    print(f'✅ 通过FlClash访问: {host}')
                else:
                    raise Exception(f'访问 {host} 需要科学上网，请确保FlClash正在运行')

            elif strategy == 'direct_preferred':
                # 国内网站：优先直连
                try:
                    response = self.try_direct_connection(url)
                    print(f'✅ 直连访问: {host}')
                except Exception as e:
                    print(f'直连失败: {e}，尝试代理连接')
                    if self.router.detector.is_flclash_available():
                        response = self.try_proxy_connection(url)
                        print(f'✅ 通过FlClash访问: {host}')
                    else:
                        raise Exception(f'无法访问 {host}：直连失败且代理不可用')

            else:
                # 未知网站：自动判断
                try:
                    response = self.try_direct_connection(url)
                    print(f'✅ 直连访问: {host}')
                except Exception as e:
                    print(f'直连失败: {e}，尝试代理连接')
                    if self.router.detector.is_flclash_available():
                        response = self.try_proxy_connection(url)
                        print(f'✅ 通过FlClash访问: {host}')
                    else:
                        raise Exception(f'无法访问 {host}：直连和代理都失败')

            # 发送响应
            self.send_response(response.getcode())
            for header, value in response.headers.items():
                if header.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header, value)
            self.end_headers()

            if self.command != 'HEAD':
                self.wfile.write(response.read())

        except Exception as e:
            print(f'HTTP错误: {e}')
            self.send_error(500, str(e))
    
    def try_direct_connect(self, host, port):
        """尝试直连HTTPS"""
        print(f'尝试直连 {host}:{port}')
        target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        target_socket.settimeout(10)
        target_socket.connect((host, port))
        return target_socket

    def try_proxy_connect(self, host, port):
        """通过FlClash代理连接HTTPS"""
        proxy_port = self.router.detector.get_proxy_port()
        print(f'通过FlClash代理连接 {host}:{port} (端口: {proxy_port})')

        target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        target_socket.settimeout(30)
        target_socket.connect(('127.0.0.1', proxy_port))

        connect_request = f'CONNECT {host}:{port} HTTP/1.1\r\nHost: {host}:{port}\r\n\r\n'
        target_socket.send(connect_request.encode())

        response = target_socket.recv(4096).decode()
        print(f'FlClash响应: {response.strip()}')
        if '200' not in response:
            raise Exception(f'FlClash代理连接失败: {response}')

        return target_socket

    def tunnel_data(self, client_socket, target_socket):
        """数据转发隧道"""
        def forward_data():
            try:
                while True:
                    ready, _, _ = select.select([client_socket, target_socket], [], [], 60)
                    if not ready:
                        print('隧道超时，关闭连接')
                        break

                    for sock in ready:
                        try:
                            data = sock.recv(4096)
                            if not data:
                                return

                            if sock is client_socket:
                                target_socket.send(data)
                            else:
                                client_socket.send(data)
                        except Exception as e:
                            print(f'数据转发错误: {e}')
                            return
            except Exception as e:
                print(f'隧道错误: {e}')
            finally:
                try:
                    target_socket.close()
                except:
                    pass

        forward_data()

    def do_CONNECT(self):
        try:
            host, port = self.path.split(':')
            port = int(port)
            strategy = self.router.get_proxy_strategy(host)

            print(f'CONNECT请求: {host}:{port} (策略: {strategy})')

            target_socket = None

            if strategy == 'proxy_required':
                # 国外网站：必须走代理
                if self.router.detector.is_flclash_available():
                    target_socket = self.try_proxy_connect(host, port)
                    print(f'✅ 通过FlClash连接: {host}:{port}')
                else:
                    raise Exception(f'访问 {host} 需要科学上网，请确保FlClash正在运行')

            elif strategy == 'direct_preferred':
                # 国内网站：优先直连
                try:
                    target_socket = self.try_direct_connect(host, port)
                    print(f'✅ 直连: {host}:{port}')
                except Exception as e:
                    print(f'直连失败: {e}，尝试代理连接')
                    if self.router.detector.is_flclash_available():
                        target_socket = self.try_proxy_connect(host, port)
                        print(f'✅ 通过FlClash连接: {host}:{port}')
                    else:
                        raise Exception(f'无法连接 {host}:{port}：直连失败且代理不可用')

            else:
                # 未知网站：自动判断
                try:
                    target_socket = self.try_direct_connect(host, port)
                    print(f'✅ 直连: {host}:{port}')
                except Exception as e:
                    print(f'直连失败: {e}，尝试代理连接')
                    if self.router.detector.is_flclash_available():
                        target_socket = self.try_proxy_connect(host, port)
                        print(f'✅ 通过FlClash连接: {host}:{port}')
                    else:
                        raise Exception(f'无法连接 {host}:{port}：直连和代理都失败')

            # 发送200响应
            self.send_response(200, 'Connection established')
            self.end_headers()

            # 开始数据转发
            client_socket = self.connection
            self.tunnel_data(client_socket, target_socket)

        except Exception as e:
            print(f'CONNECT错误: {e}')
            try:
                self.send_error(500, str(e))
            except:
                pass

class ThreadedTCPServer(socketserver.ThreadingMixIn, socketserver.TCPServer):
    allow_reuse_address = True

def main():
    PORT = 8888

    print("=" * 70)
    print("Mac端智能代理服务器启动中...")
    print(f"监听端口: {PORT}")
    print("功能: 为服务器提供智能外网访问代理")
    print("支持: HTTP/HTTPS/CONNECT协议")
    print("")
    print("🧠 智能路由功能:")
    print("   • 国内网站: 优先直连，速度更快")
    print("   • 国外网站: 通过FlClash科学上网")
    print("   • 智能回退: FlClash不可用时仍可访问国内网站")
    print("=" * 70)

    # 检测FlClash状态
    detector = ProxyDetector()
    if detector.is_flclash_available():
        port = detector.get_proxy_port()
        print(f"✅ 检测到FlClash运行在端口 {port}")
        print("🌍 支持访问: 国内网站 + 国外网站")
    else:
        print("⚠️  未检测到FlClash")
        print("🌍 支持访问: 仅国内网站")

    print("=" * 70)

    try:
        with ThreadedTCPServer(('0.0.0.0', PORT), ProxyHandler) as httpd:
            print(f'🚀 智能代理服务器运行在 0.0.0.0:{PORT}')
            print('📝 服务器配置命令:')
            print('   export http_proxy=http://您的VPN_IP:8888')
            print('   export https_proxy=http://您的VPN_IP:8888')
            print('')
            print('🔄 按 Ctrl+C 停止服务器')
            print('=' * 70)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print('\n🛑 智能代理服务器已停止')
    except Exception as e:
        print(f'❌ 启动失败: {e}')
        sys.exit(1)

if __name__ == '__main__':
    main()
