# Predixy 不支持的 Redis 命令列表

> 基于 Predixy 1.0.5 版本的实际测试结果

## 测试环境信息

- **Predixy 版本**: 1.0.5
- **后端模式**: Redis 集群模式
- **测试时间**: 2025-08-11
- **后端 Redis 节点**: 127.0.0.1:7001-7006 (3主3从)

## 完全不支持的命令

### 1. 服务器管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `TIME` | 获取服务器时间 | ERR unknown command 'time' |
| `ROLE` | 获取角色信息 | ERR unknown command 'role' |
| `DBSIZE` | 获取数据库大小 | ERR unknown command 'dbsize' |
| `FLUSHDB` | 清空当前数据库 | ERR unknown command 'flushdb' |
| `FLUSHALL` | 清空所有数据库 | ERR unknown command 'flushall' |
| `SHUTDOWN` | 关闭服务器 | ERR unknown command 'shutdown' |
| `LASTSAVE` | 最后保存时间 | ERR unknown command 'lastsave' |
| `SAVE` | 同步保存数据 | ERR unknown command 'save' |
| `BGSAVE` | 后台保存数据 | ERR unknown command 'bgsave' |
| `BGREWRITEAOF` | 后台重写AOF | ERR unknown command 'bgrewriteaof' |

### 2. Lua 脚本管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `SCRIPT EXISTS` | 检查脚本是否存在 | ERR unknown command |
| `SCRIPT FLUSH` | 清空所有脚本 | ERR unknown command 'script' |
| `SCRIPT KILL` | 终止正在执行的脚本 | ERR unknown command 'script' |
| `SCRIPT DEBUG` | 脚本调试模式 | ERR unknown command 'script' |

**注意**: `SCRIPT LOAD` 和 `EVAL` 命令是支持的。

### 3. Redis 5.0 流命令 (Streams)
| 命令 | 说明 | 状态 |
|------|------|------|
| `XADD` | 添加流条目 | 不支持 |
| `XREAD` | 读取流数据 | 不支持 |
| `XGROUP CREATE` | 创建消费组 | 不支持 |
| `XREADGROUP` | 消费组读取 | 不支持 |
| `XLEN` | 获取流长度 | 不支持 |
| `XRANGE` | 范围查询 | 不支持 |
| `XREVRANGE` | 反向范围查询 | 不支持 |

### 4. 地理位置只读命令
| 命令 | 说明 | 状态 |
|------|------|------|
| `GEORADIUS_RO` | 只读地理半径查询 | 不支持 |
| `GEORADIUSBYMEMBER_RO` | 只读成员半径查询 | 不支持 |

**注意**: 普通的 `GEOADD`、`GEORADIUS` 等命令是支持的。

### 5. 模块相关命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `MODULE LIST` | 列出已加载模块 | ERR unknown command 'module' |
| `MODULE LOAD` | 加载模块 | ERR unknown command 'module' |
| `MODULE UNLOAD` | 卸载模块 | ERR unknown command 'module' |

### 6. 调试和开发命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `DEBUG OBJECT` | 调试对象信息 | ERR unknown command 'debug' |
| `DEBUG SEGFAULT` | 触发段错误 | ERR unknown command 'debug' |
| `OBJECT ENCODING` | 对象编码信息 | ERR unknown command 'object' |
| `OBJECT FREQ` | 对象访问频率 | ERR unknown command 'object' |
| `OBJECT IDLETIME` | 对象空闲时间 | ERR unknown command 'object' |
| `OBJECT REFCOUNT` | 对象引用计数 | ERR unknown command 'object' |

### 7. 内存管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `MEMORY DOCTOR` | 内存诊断建议 | ERR unknown command 'memory' |
| `MEMORY HELP` | 内存命令帮助 | ERR unknown command 'memory' |
| `MEMORY MALLOC-STATS` | 内存分配统计 | ERR unknown command 'memory' |
| `MEMORY PURGE` | 清理内存碎片 | ERR unknown command 'memory' |
| `MEMORY STATS` | 内存使用统计 | ERR unknown command 'memory' |
| `MEMORY USAGE` | 键内存使用量 | ERR unknown command 'memory' |

### 8. 客户端管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `CLIENT LIST` | 客户端连接列表 | ERR unknown command 'client' |
| `CLIENT ID` | 获取客户端ID | ERR unknown command 'client' |
| `CLIENT KILL` | 关闭客户端连接 | ERR unknown command 'client' |
| `CLIENT GETNAME` | 获取客户端名称 | ERR unknown command 'client' |
| `CLIENT SETNAME` | 设置客户端名称 | ERR unknown command 'client' |
| `CLIENT PAUSE` | 暂停客户端命令 | ERR unknown command 'client' |
| `CLIENT REPLY` | 控制回复模式 | ERR unknown command 'client' |

### 9. 慢查询日志命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `SLOWLOG GET` | 获取慢查询日志 | ERR unknown command 'slowlog' |
| `SLOWLOG LEN` | 慢查询日志长度 | ERR unknown command 'slowlog' |
| `SLOWLOG RESET` | 重置慢查询日志 | ERR unknown command 'slowlog' |

### 10. 延迟监控命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `LATENCY DOCTOR` | 延迟诊断建议 | ERR unknown command 'latency' |
| `LATENCY GRAPH` | 延迟图表显示 | ERR unknown command 'latency' |
| `LATENCY HISTORY` | 延迟历史记录 | ERR unknown command 'latency' |
| `LATENCY LATEST` | 最新延迟事件 | ERR unknown command 'latency' |
| `LATENCY RESET` | 重置延迟监控 | ERR unknown command 'latency' |

### 11. 复制管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `SLAVEOF` | 设置主从复制 | ERR unknown command 'slaveof' |
| `REPLICAOF` | 设置复制关系 | ERR unknown command 'replicaof' |

### 12. 集群管理命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `CLUSTER INFO` | 集群信息 | ERR unknown command 'cluster' |
| `CLUSTER NODES` | 集群节点信息 | ERR unknown command 'cluster' |
| `CLUSTER MEET` | 添加节点到集群 | ERR unknown command 'cluster' |
| `CLUSTER FORGET` | 从集群移除节点 | ERR unknown command 'cluster' |
| `CLUSTER SAVECONFIG` | 保存集群配置 | ERR unknown command 'cluster' |
| `CLUSTER RESET` | 重置集群状态 | ERR unknown command 'cluster' |

### 13. 其他特殊命令
| 命令 | 说明 | 错误信息 |
|------|------|----------|
| `LOLWUT` | Redis艺术图案 | ERR unknown command 'lolwut' |
| `SWAPDB` | 交换数据库 | ERR unknown command 'swapdb' |
| `HELLO` | 握手命令(Redis 6.0+) | ERR unknown command 'hello' |

## 部分支持或行为异常的命令

### 1. CONFIG 命令系列
| 命令 | 状态 | 说明 |
|------|------|------|
| `CONFIG GET` | 支持 | 可以获取部分配置参数 |
| `CONFIG SET` | 部分支持 | 可能只支持特定参数 |
| `CONFIG REWRITE` | 部分支持 | 行为可能与直连Redis不同 |
| `CONFIG RESETSTAT` | 完全支持 | Predixy特有功能，重置统计信息 |

### 2. COMMAND 命令系列
| 命令 | 状态 | 说明 |
|------|------|------|
| `COMMAND` | 异常 | 返回空列表 |
| `COMMAND COUNT` | 异常 | 返回参数错误 |
| `COMMAND INFO` | 异常 | 返回参数错误 |
| `COMMAND GETKEYS` | 异常 | 返回参数错误 |

### 3. 连接相关命令
| 命令 | 状态 | 说明 |
|------|------|------|
| `AUTH` | 行为不同 | 被代理层处理，不直接传递给后端 |
| `QUIT` | 行为不同 | 不会真正关闭后端连接，只关闭客户端连接 |
| `ECHO` | 正常 | 正常工作 |
| `PING` | 正常 | 正常工作 |

## 完全支持的核心命令

### 数据操作命令
- **字符串操作**: GET, SET, INCR, DECR, APPEND 等
- **哈希操作**: HGET, HSET, HMGET, HMSET, HDEL 等
- **列表操作**: LPUSH, RPUSH, LPOP, RPOP, LLEN 等
- **集合操作**: SADD, SREM, SMEMBERS, SINTER 等
- **有序集合**: ZADD, ZREM, ZRANGE, ZRANK 等
- **地理位置**: GEOADD, GEORADIUS, GEODIST 等（除只读命令）

### 事务和脚本
- **事务**: MULTI, EXEC, DISCARD, WATCH, UNWATCH
- **脚本**: EVAL, SCRIPT LOAD（但不支持 SCRIPT 管理命令）

### 发布订阅
- **发布订阅**: PUBLISH, SUBSCRIBE, UNSUBSCRIBE, PUBSUB 等

### 多键操作
- **多键命令**: MGET, MSET, DEL, EXISTS 等

## 设计理念说明

Predixy 作为 Redis 代理，其命令支持策略遵循以下原则：

1. **数据操作优先**: 完全支持所有数据读写操作命令
2. **管理命令屏蔽**: 屏蔽大部分服务器管理和调试命令，确保安全性
3. **代理层功能**: 提供自己的管理功能（如统计信息重置）
4. **集群抽象**: 隐藏后端集群的复杂性，提供统一的访问接口
5. **安全考虑**: 防止客户端直接操作后端 Redis 服务器

## 使用建议

1. **应用开发**: 使用 Predixy 时，专注于数据操作命令，避免使用管理命令
2. **监控运维**: 通过 Predixy 的 INFO 命令获取代理层统计信息
3. **调试排查**: 需要调试时，可以直接连接后端 Redis 节点
4. **版本升级**: 不同版本的 Predixy 支持的命令可能有差异，升级前需要测试

---

*本文档基于 Predixy 1.0.5 的实际测试结果生成，测试时间：2025-08-11*
