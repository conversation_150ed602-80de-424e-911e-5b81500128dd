#!/bin/bash

# Predixy 不支持命令测试脚本
# 测试所有可能不支持或有限制的 Redis 命令

set -uo pipefail

# 配置
PREDIXY_PORT=6379  # 修改为你的 predixy 端口
REDIS_HOST="127.0.0.1"
TEST_TIMEOUT=3

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 查找 redis-cli
find_redis_cli() {
    local redis_cli_paths=(
        "redis-cli"
        "/usr/local/bin/redis-cli"
        "/opt/homebrew/bin/redis-cli"
        "/usr/bin/redis-cli"
        "$(which redis-cli 2>/dev/null)"
    )
    
    for path in "${redis_cli_paths[@]}"; do
        if command -v "$path" &> /dev/null; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# 测试命令函数
test_command() {
    local cmd="$1"
    local description="$2"
    local expected_behavior="$3"  # "support" 或 "unsupport"

    printf "%-50s " "$description"

    local output
    output=$($REDIS_CLI $cmd 2>&1)
    local exit_code=$?

    # 检查是否是 "unknown command" 错误
    if [[ $output == *"unknown command"* ]] || [[ $output == *"ERR unknown command"* ]]; then
        if [[ "$expected_behavior" == "unsupport" ]]; then
            log_error "不支持 (预期)"
        else
            log_error "不支持 (意外)"
        fi
        return 1
    elif [[ $exit_code -eq 0 ]]; then
        if [[ "$expected_behavior" == "support" ]]; then
            log_success "支持"
        else
            log_warning "意外支持"
        fi
        return 0
    else
        # 其他错误（如参数错误等）
        if [[ "$expected_behavior" == "support" ]]; then
            log_warning "支持但有错误"
            echo "    错误: $output"
        else
            log_error "不支持 (预期)"
        fi
        return 1
    fi
}

# 测试带输出的命令
test_command_with_output() {
    local cmd="$1"
    local description="$2"

    printf "%-50s " "$description"

    local output
    output=$($REDIS_CLI $cmd 2>&1)
    local exit_code=$?

    # 检查是否是 "unknown command" 错误
    if [[ $output == *"unknown command"* ]] || [[ $output == *"ERR unknown command"* ]]; then
        log_error "不支持"
        echo "    错误: $output"
        return 1
    elif [[ $exit_code -eq 0 ]]; then
        log_success "支持"
        echo "    输出: $output"
        return 0
    else
        log_error "执行失败"
        echo "    错误: $output"
        return 1
    fi
}

# 测试阻塞命令（需要 timeout）
test_blocking_command() {
    local cmd="$1"
    local description="$2"
    local expected_behavior="$3"

    printf "%-50s " "$description"

    local output
    local timeout_cmd=""

    # 检查可用的 timeout 命令
    if command -v timeout &>/dev/null; then
        timeout_cmd="timeout 2"
    elif command -v gtimeout &>/dev/null; then
        timeout_cmd="gtimeout 2"
    else
        log_warning "跳过 (无timeout命令)"
        return 0
    fi

    # 执行带超时的命令
    output=$($timeout_cmd $REDIS_CLI $cmd 2>&1)
    local exit_code=$?

    # 检查结果
    if [[ $output == *"unknown command"* ]] || [[ $output == *"ERR unknown command"* ]]; then
        if [[ "$expected_behavior" == "unsupport" ]]; then
            log_error "不支持 (预期)"
        else
            log_error "不支持 (意外)"
        fi
        return 1
    elif [[ $exit_code -eq 124 ]] || [[ $exit_code -eq 143 ]]; then
        # timeout 退出码 124，或被 SIGTERM 终止 (143)
        if [[ "$expected_behavior" == "support" ]]; then
            log_success "支持 (超时正常)"
        else
            log_warning "意外支持 (超时)"
        fi
        return 0
    elif [[ $exit_code -eq 0 ]]; then
        if [[ "$expected_behavior" == "support" ]]; then
            log_success "支持"
        else
            log_warning "意外支持"
        fi
        return 0
    else
        if [[ "$expected_behavior" == "unsupport" ]]; then
            log_error "不支持 (预期)"
        else
            log_error "不支持 (意外)"
        fi
        return 1
    fi
}

# 主函数
main() {
    log_info "Predixy 不支持命令测试开始"
    log_info "目标: $REDIS_HOST:$PREDIXY_PORT"
    
    # 查找 redis-cli
    if ! REDIS_CLI=$(find_redis_cli); then
        log_error "找不到 redis-cli 命令"
        exit 1
    fi
    
    REDIS_CLI="$REDIS_CLI -h $REDIS_HOST -p $PREDIXY_PORT"
    log_info "使用 redis-cli: $REDIS_CLI"
    
    # 测试连接
    log_info "测试连接..."
    if ! $REDIS_CLI ping &>/dev/null; then
        log_error "无法连接到 $REDIS_HOST:$PREDIXY_PORT"
        exit 1
    fi
    log_success "连接成功"
    
    echo
    log_info "开始测试各类命令..."
    
    # 1. 连接和认证命令
    echo
    log_info "=== 1. 连接和认证命令 ==="
    test_command "QUIT" "QUIT - 退出连接" "unsupport"
    test_command_with_output "ECHO hello" "ECHO - 回显消息"
    test_command_with_output "PING" "PING - 连接测试"
    test_command "AUTH wrongpassword" "AUTH - 认证" "unsupport"
    test_command "HELLO 3" "HELLO - 握手命令" "unsupport"
    
    # 2. 事务命令（集群模式限制）
    echo
    log_info "=== 2. 事务命令 (集群模式可能不支持) ==="
    test_command "MULTI" "MULTI - 开始事务" "support"
    test_command "EXEC" "EXEC - 执行事务" "support"
    test_command "DISCARD" "DISCARD - 取消事务" "support"
    test_command "WATCH nonexistentkey" "WATCH - 监控键" "support"
    test_command "UNWATCH" "UNWATCH - 取消监控" "support"
    
    # 3. Lua 脚本命令
    echo
    log_info "=== 3. Lua 脚本命令 ==="
    test_command_with_output "EVAL \"return 'hello'\" 0" "EVAL - 执行脚本"
    test_command "SCRIPT LOAD \"return 'hello'\"" "SCRIPT LOAD - 加载脚本" "support"
    test_command_with_output "SCRIPT EXISTS nonexistentsha" "SCRIPT EXISTS - 检查脚本"
    test_command "SCRIPT FLUSH" "SCRIPT FLUSH - 清空脚本" "unsupport"
    test_command "SCRIPT KILL" "SCRIPT KILL - 终止脚本" "unsupport"
    test_command "SCRIPT DEBUG YES" "SCRIPT DEBUG - 脚本调试" "unsupport"
    
    # 4. 发布订阅命令
    echo
    log_info "=== 4. 发布订阅命令 ==="
    test_command_with_output "PUBLISH testchannel hello" "PUBLISH - 发布消息"
    # 注意：SUBSCRIBE 会阻塞，所以用特殊函数
    test_blocking_command "SUBSCRIBE testchannel" "SUBSCRIBE - 订阅频道" "support"
    test_blocking_command "PSUBSCRIBE test*" "PSUBSCRIBE - 模式订阅" "support"
    test_command_with_output "PUBSUB CHANNELS" "PUBSUB CHANNELS - 频道列表"
    test_command_with_output "PUBSUB NUMSUB testchannel" "PUBSUB NUMSUB - 订阅者数量"
    test_command_with_output "PUBSUB NUMPAT" "PUBSUB NUMPAT - 模式数量"
    
    # 5. 流命令（Redis 5.0+）
    echo
    log_info "=== 5. 流命令 (Redis 5.0+) ==="
    test_command "XADD teststream * field value" "XADD - 添加流条目" "support"
    test_command "XREAD STREAMS teststream 0" "XREAD - 读取流" "support"
    test_command "XGROUP CREATE teststream testgroup 0" "XGROUP CREATE - 创建消费组" "support"
    test_command "XREADGROUP GROUP testgroup consumer1 STREAMS teststream >" "XREADGROUP - 组读取" "support"

    # 6. 地理位置命令
    echo
    log_info "=== 6. 地理位置命令 ==="
    test_command "GEOADD testgeo 13.361389 38.115556 Palermo" "GEOADD - 添加地理位置" "support"
    test_command "GEORADIUS_RO testgeo 15 37 200 km" "GEORADIUS_RO - 只读地理半径查询" "unsupport"
    test_command "GEORADIUSBYMEMBER_RO testgeo Palermo 200 km" "GEORADIUSBYMEMBER_RO - 只读成员半径查询" "unsupport"

    # 7. 模块相关命令
    echo
    log_info "=== 7. 模块相关命令 ==="
    test_command_with_output "MODULE LIST" "MODULE LIST - 模块列表"
    test_command "MODULE LOAD /path/to/module.so" "MODULE LOAD - 加载模块" "unsupport"
    test_command "MODULE UNLOAD testmodule" "MODULE UNLOAD - 卸载模块" "unsupport"

    # 8. 调试和开发命令
    echo
    log_info "=== 8. 调试和开发命令 ==="
    # 先创建一个测试键
    $REDIS_CLI SET debugkey "testvalue" &>/dev/null
    test_command "DEBUG OBJECT debugkey" "DEBUG OBJECT - 调试对象" "unsupport"
    test_command "DEBUG SEGFAULT" "DEBUG SEGFAULT - 触发段错误" "unsupport"
    test_command "OBJECT ENCODING debugkey" "OBJECT ENCODING - 对象编码" "unsupport"
    test_command "OBJECT FREQ debugkey" "OBJECT FREQ - 对象频率" "unsupport"
    test_command "OBJECT IDLETIME debugkey" "OBJECT IDLETIME - 对象空闲时间" "unsupport"
    test_command "OBJECT REFCOUNT debugkey" "OBJECT REFCOUNT - 对象引用计数" "unsupport"

    # 9. 跨节点多键命令（集群模式限制）
    echo
    log_info "=== 9. 跨节点多键命令 (集群模式可能限制) ==="
    test_command "MSET key1 val1 key2 val2" "MSET - 多键设置" "support"
    test_command "MSETNX key3 val3 key4 val4" "MSETNX - 多键条件设置" "support"
    test_command "MGET key1 key2" "MGET - 多键获取" "support"
    test_command "DEL key1 key2" "DEL - 多键删除" "support"
    test_command "EXISTS key1 key2" "EXISTS - 多键存在检查" "support"
    test_command "UNLINK key3 key4" "UNLINK - 多键异步删除" "support"
    test_command "TOUCH key1 key2" "TOUCH - 多键更新访问时间" "support"

    # 10. 阻塞命令限制
    echo
    log_info "=== 10. 阻塞命令 ==="
    # 先准备一些测试数据
    $REDIS_CLI LPUSH testlist1 "item1" &>/dev/null
    $REDIS_CLI LPUSH testlist2 "item2" &>/dev/null
    $REDIS_CLI ZADD testzset1 1 "member1" &>/dev/null
    $REDIS_CLI ZADD testzset2 2 "member2" &>/dev/null

    test_blocking_command "BLPOP testlist1 testlist2 1" "BLPOP - 多键阻塞左弹出" "support"
    test_blocking_command "BRPOP testlist1 testlist2 1" "BRPOP - 多键阻塞右弹出" "support"
    test_blocking_command "BRPOPLPUSH testlist1 testlist2 1" "BRPOPLPUSH - 阻塞右弹左推" "support"
    test_blocking_command "BZPOPMIN testzset1 testzset2 1" "BZPOPMIN - 阻塞有序集最小弹出" "support"
    test_blocking_command "BZPOPMAX testzset1 testzset2 1" "BZPOPMAX - 阻塞有序集最大弹出" "support"

    # 11. 管理命令（应该大部分不支持）
    echo
    log_info "=== 11. 管理命令 ==="
    test_command_with_output "INFO" "INFO - 服务器信息"
    test_command_with_output "TIME" "TIME - 服务器时间"
    test_command "ROLE" "ROLE - 角色信息" "unsupport"
    test_command "DBSIZE" "DBSIZE - 数据库大小" "support"
    test_command "FLUSHDB" "FLUSHDB - 清空数据库" "unsupport"
    test_command "FLUSHALL" "FLUSHALL - 清空所有数据库" "unsupport"
    test_command "SHUTDOWN" "SHUTDOWN - 关闭服务器" "unsupport"
    test_command "LASTSAVE" "LASTSAVE - 最后保存时间" "unsupport"
    test_blocking_command "MONITOR" "MONITOR - 监控命令" "unsupport"
    test_command "SAVE" "SAVE - 同步保存" "unsupport"
    test_command "BGSAVE" "BGSAVE - 后台保存" "unsupport"
    test_command "BGREWRITEAOF" "BGREWRITEAOF - 后台重写AOF" "unsupport"

    # 12. CONFIG 命令
    echo
    log_info "=== 12. CONFIG 命令 ==="
    test_command_with_output "CONFIG GET maxmemory" "CONFIG GET - 获取配置"
    test_command "CONFIG SET maxmemory 100mb" "CONFIG SET - 设置配置" "unsupport"
    test_command "CONFIG REWRITE" "CONFIG REWRITE - 重写配置" "unsupport"
    test_command_with_output "CONFIG RESETSTAT" "CONFIG RESETSTAT - 重置统计"

    # 13. COMMAND 系列
    echo
    log_info "=== 13. COMMAND 系列 ==="
    test_command_with_output "COMMAND" "COMMAND - 命令列表"
    test_command_with_output "COMMAND COUNT" "COMMAND COUNT - 命令数量"
    test_command_with_output "COMMAND INFO GET SET" "COMMAND INFO - 命令信息"
    test_command_with_output "COMMAND GETKEYS MSET key1 val1 key2 val2" "COMMAND GETKEYS - 获取键参数"

    # 14. MEMORY 系列
    echo
    log_info "=== 14. MEMORY 系列 ==="
    test_command "MEMORY DOCTOR" "MEMORY DOCTOR - 内存诊断" "unsupport"
    test_command "MEMORY HELP" "MEMORY HELP - 内存帮助" "unsupport"
    test_command "MEMORY MALLOC-STATS" "MEMORY MALLOC-STATS - 内存分配统计" "unsupport"
    test_command "MEMORY PURGE" "MEMORY PURGE - 内存清理" "unsupport"
    test_command "MEMORY STATS" "MEMORY STATS - 内存统计" "unsupport"
    test_command "MEMORY USAGE debugkey" "MEMORY USAGE - 键内存使用" "unsupport"

    # 15. 客户端管理
    echo
    log_info "=== 15. 客户端管理 ==="
    test_command_with_output "CLIENT LIST" "CLIENT LIST - 客户端列表"
    test_command_with_output "CLIENT ID" "CLIENT ID - 客户端ID"
    test_command "CLIENT KILL 127.0.0.1:12345" "CLIENT KILL - 关闭客户端" "unsupport"
    test_command_with_output "CLIENT GETNAME" "CLIENT GETNAME - 获取客户端名称"
    test_command "CLIENT SETNAME testclient" "CLIENT SETNAME - 设置客户端名称" "support"
    test_command "CLIENT PAUSE 1000" "CLIENT PAUSE - 暂停客户端" "unsupport"
    test_command "CLIENT REPLY OFF" "CLIENT REPLY - 控制回复" "unsupport"

    # 16. 慢查询日志
    echo
    log_info "=== 16. 慢查询日志 ==="
    test_command_with_output "SLOWLOG GET 5" "SLOWLOG GET - 获取慢查询"
    test_command_with_output "SLOWLOG LEN" "SLOWLOG LEN - 慢查询长度"
    test_command "SLOWLOG RESET" "SLOWLOG RESET - 重置慢查询" "unsupport"

    # 17. 延迟监控
    echo
    log_info "=== 17. 延迟监控 ==="
    test_command "LATENCY DOCTOR" "LATENCY DOCTOR - 延迟诊断" "unsupport"
    test_command "LATENCY GRAPH command" "LATENCY GRAPH - 延迟图表" "unsupport"
    test_command "LATENCY HISTORY command" "LATENCY HISTORY - 延迟历史" "unsupport"
    test_command "LATENCY LATEST" "LATENCY LATEST - 最新延迟" "unsupport"
    test_command "LATENCY RESET" "LATENCY RESET - 重置延迟" "unsupport"

    # 18. 复制管理
    echo
    log_info "=== 18. 复制管理 ==="
    test_command "SLAVEOF 127.0.0.1 6380" "SLAVEOF - 设置主从" "unsupport"
    test_command "SLAVEOF NO ONE" "SLAVEOF NO ONE - 停止复制" "unsupport"
    test_command "REPLICAOF 127.0.0.1 6380" "REPLICAOF - 设置复制" "unsupport"
    test_command "REPLICAOF NO ONE" "REPLICAOF NO ONE - 停止复制" "unsupport"

    # 19. 集群管理
    echo
    log_info "=== 19. 集群管理 ==="
    test_command_with_output "CLUSTER INFO" "CLUSTER INFO - 集群信息"
    test_command_with_output "CLUSTER NODES" "CLUSTER NODES - 集群节点"
    test_command "CLUSTER MEET 127.0.0.1 7000" "CLUSTER MEET - 添加节点" "unsupport"
    test_command "CLUSTER FORGET nodeid" "CLUSTER FORGET - 移除节点" "unsupport"
    test_command "CLUSTER SAVECONFIG" "CLUSTER SAVECONFIG - 保存配置" "unsupport"
    test_command "CLUSTER RESET" "CLUSTER RESET - 重置集群" "unsupport"

    # 20. 其他特殊命令
    echo
    log_info "=== 20. 其他特殊命令 ==="
    test_command_with_output "LOLWUT" "LOLWUT - Redis艺术图案"
    test_command "SWAPDB 0 1" "SWAPDB - 交换数据库" "unsupport"
    test_command "SELECT 1" "SELECT - 选择数据库" "support"

    # 清理测试数据
    echo
    log_info "=== 清理测试数据 ==="
    $REDIS_CLI DEL debugkey testlist1 testlist2 testzset1 testzset2 teststream testgeo &>/dev/null
    $REDIS_CLI SELECT 0 &>/dev/null

    echo
    log_info "测试完成！"
    log_info "注意：某些命令的支持情况可能因 predixy 配置和后端 Redis 模式而异"
}

# 运行主函数
main "$@"
