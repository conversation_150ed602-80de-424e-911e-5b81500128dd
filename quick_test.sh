#!/bin/bash

# 快速测试 predixy 命令支持情况
# 简化版本，只测试最重要的命令

PREDIXY_PORT=6379  # 修改为你的端口
REDIS_CLI="redis-cli -p $PREDIXY_PORT"

echo "=== Predixy 命令支持快速测试 ==="
echo "端口: $PREDIXY_PORT"
echo

# 测试连接
echo "测试连接..."
if ! $REDIS_CLI ping &>/dev/null; then
    echo "❌ 连接失败"
    exit 1
fi
echo "✅ 连接成功"
echo

# 定义测试函数
test_cmd() {
    local cmd="$1"
    local desc="$2"
    printf "%-40s " "$desc"

    if $REDIS_CLI $cmd &>/dev/null; then
        echo "✅ 支持"
        return 0
    else
        echo "❌ 不支持"
        return 1
    fi
}

test_cmd_output() {
    local cmd="$1"
    local desc="$2"
    printf "%-40s " "$desc"

    local output
    if output=$($REDIS_CLI $cmd 2>&1); then
        echo "✅ 支持"
        echo "    → $output"
        return 0
    else
        echo "❌ 不支持"
        echo "    → $output"
        return 1
    fi
}

echo "=== 基础数据命令 ==="
test_cmd "SET testkey hello" "SET - 设置键值"
test_cmd_output "GET testkey" "GET - 获取键值"
echo

echo "=== 连接命令 ==="
test_cmd "QUIT" "QUIT - 退出连接"
test_cmd_output "ECHO hello" "ECHO - 回显消息"
test_cmd "AUTH wrongpass" "AUTH - 认证"
echo

echo "=== 事务命令 ==="
test_cmd "MULTI" "MULTI - 开始事务"
test_cmd "DISCARD" "DISCARD - 取消事务"
test_cmd "WATCH testkey" "WATCH - 监控键"
test_cmd "UNWATCH" "UNWATCH - 取消监控"
echo

echo "=== 脚本命令 ==="
test_cmd_output "EVAL \"return 'hello'\" 0" "EVAL - 执行脚本"
test_cmd "SCRIPT LOAD \"return 1\"" "SCRIPT LOAD - 加载脚本"
test_cmd "SCRIPT FLUSH" "SCRIPT FLUSH - 清空脚本"
echo

echo "=== 发布订阅 ==="
test_cmd_output "PUBLISH testch msg" "PUBLISH - 发布消息"
test_cmd_output "PUBSUB CHANNELS" "PUBSUB CHANNELS - 频道列表"
echo

echo "=== 多键命令 ==="
test_cmd "MSET k1 v1 k2 v2" "MSET - 多键设置"
test_cmd_output "MGET k1 k2" "MGET - 多键获取"
test_cmd "DEL k1 k2" "DEL - 多键删除"
echo

echo "=== 阻塞命令 ==="
$REDIS_CLI LPUSH testlist item &>/dev/null
test_cmd "BLPOP testlist 1" "BLPOP - 阻塞弹出"
echo

echo "=== 管理命令 ==="
test_cmd_output "INFO" "INFO - 服务器信息"
test_cmd_output "TIME" "TIME - 服务器时间"
test_cmd "ROLE" "ROLE - 角色信息"
test_cmd_output "DBSIZE" "DBSIZE - 数据库大小"
test_cmd "FLUSHDB" "FLUSHDB - 清空数据库"
test_cmd "MONITOR" "MONITOR - 监控命令"
test_cmd "SHUTDOWN" "SHUTDOWN - 关闭服务器"
echo

echo "=== CONFIG 命令 ==="
test_cmd_output "CONFIG GET maxmemory" "CONFIG GET - 获取配置"
test_cmd "CONFIG SET maxmemory 100mb" "CONFIG SET - 设置配置"
test_cmd_output "CONFIG RESETSTAT" "CONFIG RESETSTAT - 重置统计"
echo

echo "=== COMMAND 系列 ==="
test_cmd_output "COMMAND" "COMMAND - 命令列表"
test_cmd_output "COMMAND COUNT" "COMMAND COUNT - 命令数量"
test_cmd_output "COMMAND INFO GET" "COMMAND INFO - 命令信息"
echo

echo "=== MEMORY 系列 ==="
test_cmd "MEMORY STATS" "MEMORY STATS - 内存统计"
test_cmd "MEMORY DOCTOR" "MEMORY DOCTOR - 内存诊断"
test_cmd "MEMORY USAGE testkey" "MEMORY USAGE - 键内存使用"
echo

echo "=== 客户端管理 ==="
test_cmd_output "CLIENT LIST" "CLIENT LIST - 客户端列表"
test_cmd_output "CLIENT ID" "CLIENT ID - 客户端ID"
test_cmd "CLIENT PAUSE 100" "CLIENT PAUSE - 暂停客户端"
echo

echo "=== 慢查询 ==="
test_cmd_output "SLOWLOG LEN" "SLOWLOG LEN - 慢查询长度"
test_cmd_output "SLOWLOG GET 1" "SLOWLOG GET - 获取慢查询"
test_cmd "SLOWLOG RESET" "SLOWLOG RESET - 重置慢查询"
echo

echo "=== 调试命令 ==="
test_cmd "DEBUG OBJECT testkey" "DEBUG OBJECT - 调试对象"
test_cmd "OBJECT ENCODING testkey" "OBJECT ENCODING - 对象编码"
echo

echo "=== 集群命令 ==="
test_cmd_output "CLUSTER INFO" "CLUSTER INFO - 集群信息"
test_cmd_output "CLUSTER NODES" "CLUSTER NODES - 集群节点"
echo

echo "=== 其他命令 ==="
test_cmd_output "LOLWUT" "LOLWUT - Redis艺术"
test_cmd "SWAPDB 0 1" "SWAPDB - 交换数据库"
test_cmd_output "SELECT 0" "SELECT - 选择数据库"
echo

# 清理
$REDIS_CLI DEL testkey testlist &>/dev/null

echo "=== 测试完成 ==="
echo "注意：结果可能因 predixy 配置和后端模式而异"
